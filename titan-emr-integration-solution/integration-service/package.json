{"name": "integration-service", "version": "4.0.0", "private": true, "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "test": "jest", "debug": "node --inspect --loader ts-node/esm src/index.ts", "dev": "nodemon --watch src --ext ts,json --exec \"node --loader ts-node/esm src/index.ts\"", "audit": "npm audit --audit-level=high", "audit:fix": "npm audit fix"}, "dependencies": {"axios": "^1.7.4", "body-parser": "^1.20.2", "dotenv": "^16.4.0", "express": "^4.19.2", "morgan": "^1.10.0", "ts-node": "^10.9.2", "winston": "^3.14.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.5.5", "@types/morgan": "^1.9.6", "@types/node": "^20.11.30", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.1.1", "tsx": "^4.19.0", "typescript": "^5.5.4"}}