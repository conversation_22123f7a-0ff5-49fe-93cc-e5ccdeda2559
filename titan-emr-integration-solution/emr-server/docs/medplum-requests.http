### 1. Get Medplum token (direct API)
POST https://api.medplum.com/oauth2/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials
&client_id={{MEDPLUM_CLIENT_ID}}
&client_secret={{MEDPLUM_CLIENT_SECRET}}
&scope=system/*.read system/*.write

### 2. List Patients directly in Medplum
# Replace {{medplum_token}} with the access_token returned above
GET https://api.medplum.com/fhir/R4/Patient
Authorization: Bearer {{medplum_token}}

### 3. Get EMR server token (local)
POST http://localhost:8081/auth/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&client_id=demo&client_secret=demo

### 4. Send sample HL7 to EMR server (upsert → Medplum)
# Replace {{emr_token}} with access_token from step 3
POST http://localhost:8081/hl7/inbound
Authorization: Bearer {{emr_token}}
Content-Type: text/plain

MSH|^~\&|INTEGRATION|TITAN|EMR|MEDPLUM|202409201200||ADT^A04|MSG00001|P|2.5
PID|||12345^^^MRN||Smith^John||20031231|M|||123 Main St^^Houston^TX^77001||**********
IN1|1|bluecross|||...||||GROUP1||||||||||PPO|||||||||||||||||234ASD
OBX|1|NM|HR||72|/min|||N|||202409201159
OBX|2|ST|Notes||Patient feels well|||||202409201200
