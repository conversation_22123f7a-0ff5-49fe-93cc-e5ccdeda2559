### 1. Get local EMR token
POST http://localhost:8081/auth/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&client_id=demo&client_secret=demo

### 2. Send sample HL7 to EMR server (will upsert into Medplum if configured in .env)
# Replace {{emr_token}} with access_token from step 1
POST http://localhost:8081/hl7/inbound
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.kmn-g2AgwUunr28NYkonJVj7v4udFoefs5QJuEkfExg
Content-Type: text/plain

MSH|^~\&|INTEGRATION|TITAN|EMR|MEDPLUM|202409201200||ADT^A04|MSG00001|P|2.5
PID|||12345^^^MRN||Smith^John||20031231|M|||123 Main St^^Houston^TX^77001||**********
IN1|1|bluecross|||...||||GROUP1||||||||||PPO|||||||||||||||||234ASD
OBX|1|NM|HR||72|/min|||N|||202409201159
OBX|2|ST|Notes||Patient feels well|||||202409201200

### 3. Get Medplum token directly
POST https://api.medplum.com/oauth2/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials
&client_id={{MEDPLUM_CLIENT_ID}}
&client_secret={{MEDPLUM_CLIENT_SECRET}}
&scope=system/*.read system/*.write

### 4. Query Medplum for Patient 12345
# Replace {{medplum_token}} with access_token from step 3
GET https://api.medplum.com/fhir/R4/Patient?identifier=MRN|12345
Authorization: Bearer {{medplum_token}}
