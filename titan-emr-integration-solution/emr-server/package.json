{"name": "emr-server", "version": "4.0.0", "private": true, "type": "module", "scripts": {"build": "tsc -p . && cp -r src/schemas dist/", "start": "node dist/index.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "debug": "node --inspect --loader ts-node/esm src/index.ts", "dev": "nodemon --watch src --ext ts,json --exec \"node --loader ts-node/esm src/index.ts\"", "audit": "npm audit --audit-level=high", "audit:fix": "npm audit fix"}, "dependencies": {"@medplum/core": "^4.3.14", "@medplum/fhirtypes": "^4.3.14", "@medplum/react": "^4.3.14", "ajv": "^8.12.0", "axios": "^1.7.4", "body-parser": "^1.20.2", "dotenv": "^16.6.1", "express": "^4.19.2", "express-jwt": "^8.4.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "simple-hl7": "^2.2.2", "ts-node": "^10.9.2", "uuid": "^9.0.1", "winston": "^3.14.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/mocha": "^10.0.10", "@types/morgan": "^1.9.6", "@types/node": "^20.11.30", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.1.1", "tsx": "^4.19.0", "typescript": "^5.5.4"}}